import * as THREE from 'three';
import { GameState, GameConfig, Vector3 } from '@/types';

export class GameEngine {
  private gameState: GameState;
  private config: GameConfig;
  private clock: THREE.Clock;
  private animationId: number | null = null;
  private updateCallbacks: (() => void)[] = [];

  constructor(canvas: HTMLCanvasElement) {
    this.clock = new THREE.Clock();
    this.config = this.getDefaultConfig();
    this.gameState = this.initializeGameState(canvas);
    
    this.setupRenderer();
    this.setupScene();
    this.setupCamera();
    this.setupLighting();
  }

  private getDefaultConfig(): GameConfig {
    return {
      renderDistance: 8,
      chunkSize: 16,
      worldHeight: 256,
      gravity: -9.8,
      jumpForce: 5,
      moveSpeed: 5,
      mouseSensitivity: 0.002
    };
  }

  private initializeGameState(canvas: HTMLCanvasElement): GameState {
    const renderer = new THREE.WebGLRenderer({ 
      canvas,
      antialias: true,
      alpha: false 
    });
    
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(
      75, 
      window.innerWidth / window.innerHeight, 
      0.1, 
      1000
    );

    return {
      player: {
        position: { x: 0, y: 10, z: 0 },
        rotation: { x: 0, y: 0 },
        velocity: { x: 0, y: 0, z: 0 },
        onGround: false,
        selectedBlockType: 1 // GRASS
      },
      chunks: new Map(),
      camera,
      scene,
      renderer,
      isPointerLocked: false,
      keys: new Set()
    };
  }

  private setupRenderer(): void {
    const { renderer } = this.gameState;

    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    renderer.setClearColor(0x87CEEB, 1); // Sky blue

    // 启用雾效果
    this.gameState.scene.fog = new THREE.Fog(0x87CEEB, 50, 200);
  }

  private setupScene(): void {
    const { scene } = this.gameState;
    
    // 添加天空盒
    const skyGeometry = new THREE.SphereGeometry(500, 32, 32);
    const skyMaterial = new THREE.MeshBasicMaterial({
      color: 0x87CEEB,
      side: THREE.BackSide
    });
    const sky = new THREE.Mesh(skyGeometry, skyMaterial);
    scene.add(sky);
  }

  private setupCamera(): void {
    const { camera, player } = this.gameState;
    
    camera.position.set(
      player.position.x,
      player.position.y + 1.8, // 玩家眼睛高度
      player.position.z
    );
    
    camera.rotation.order = 'YXZ';
  }

  private setupLighting(): void {
    const { scene } = this.gameState;

    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);

    // 方向光（太阳光）
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(50, 100, 50);
    directionalLight.castShadow = true;

    // 配置阴影
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 500;
    directionalLight.shadow.camera.left = -100;
    directionalLight.shadow.camera.right = 100;
    directionalLight.shadow.camera.top = 100;
    directionalLight.shadow.camera.bottom = -100;

    scene.add(directionalLight);
  }

  public start(): void {
    console.log('🎬 Starting game loop...');
    try {
      this.gameLoop();
      console.log('✅ Game loop started successfully');
    } catch (error) {
      console.error('❌ Error starting game loop:', error);
      throw error;
    }
  }

  public stop(): void {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }

  private gameLoop = (): void => {
    try {
      this.animationId = requestAnimationFrame(this.gameLoop);

      const deltaTime = this.clock.getDelta();

      this.update(deltaTime);
      this.render();

      // 只在第一帧打印成功信息
      if (!this.firstFrameRendered) {
        console.log('✅ First frame rendered successfully');
        this.firstFrameRendered = true;
      }
    } catch (error) {
      console.error('❌ Error in game loop:', error);
      console.error('Error details:', error);
      this.stop();
    }
  };

  private firstFrameRendered = false;

  private update(deltaTime: number): void {
    try {
      // 更新玩家位置
      this.updatePlayer(deltaTime);

      // 更新相机位置
      this.updateCamera();

      // 执行注册的更新回调
      this.updateCallbacks.forEach(callback => callback());
    } catch (error) {
      console.error('❌ Error in update:', error);
      throw error;
    }
  }

  private updatePlayer(deltaTime: number): void {
    const { player } = this.gameState;
    
    // 应用重力
    if (!player.onGround) {
      player.velocity.y += this.config.gravity * deltaTime;
    }
    
    // 更新位置
    player.position.x += player.velocity.x * deltaTime;
    player.position.y += player.velocity.y * deltaTime;
    player.position.z += player.velocity.z * deltaTime;
    
    // 简单的地面碰撞检测
    if (player.position.y < 1) {
      player.position.y = 1;
      player.velocity.y = 0;
      player.onGround = true;
    } else {
      player.onGround = false;
    }
  }

  private updateCamera(): void {
    const { camera, player } = this.gameState;
    
    camera.position.set(
      player.position.x,
      player.position.y + 1.8,
      player.position.z
    );
    
    camera.rotation.x = player.rotation.x;
    camera.rotation.y = player.rotation.y;
  }

  private render(): void {
    try {
      const { renderer, scene, camera } = this.gameState;
      renderer.render(scene, camera);
    } catch (error) {
      console.error('❌ Error in render:', error);
      throw error;
    }
  }

  public getGameState(): GameState {
    return this.gameState;
  }

  public getConfig(): GameConfig {
    return this.config;
  }

  public resize(width: number, height: number): void {
    const { camera, renderer } = this.gameState;

    camera.aspect = width / height;
    camera.updateProjectionMatrix();
    renderer.setSize(width, height);
  }

  public addUpdateCallback(callback: () => void): void {
    this.updateCallbacks.push(callback);
  }

  public removeUpdateCallback(callback: () => void): void {
    const index = this.updateCallbacks.indexOf(callback);
    if (index > -1) {
      this.updateCallbacks.splice(index, 1);
    }
  }
}
