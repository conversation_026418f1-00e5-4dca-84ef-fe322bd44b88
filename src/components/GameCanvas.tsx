import React, { useRef, useEffect, useState } from 'react';
import { GameEngine } from '@/engine/GameEngine';
import { InputSystem } from '@/systems/InputSystem';
import { BlockRenderer } from '@/systems/BlockRenderer';
import { BlockInteraction } from '@/systems/BlockInteraction';
import { WorldManager } from '@/systems/WorldManager';
import { BlockType } from '@/types';
import * as THREE from 'three';

interface GameCanvasProps {
  className?: string;
}

export const GameCanvas: React.FC<GameCanvasProps> = ({ className }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const gameEngineRef = useRef<GameEngine | null>(null);
  const inputSystemRef = useRef<InputSystem | null>(null);
  const blockRendererRef = useRef<BlockRenderer | null>(null);
  const blockInteractionRef = useRef<BlockInteraction | null>(null);
  const worldManagerRef = useRef<WorldManager | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeGame = async () => {
      try {
        console.log('🎮 Starting game initialization...');

        if (!canvasRef.current) {
          throw new Error('Canvas element not found');
        }
        console.log('✅ Canvas found');

        // 添加延迟确保DOM完全加载
        await new Promise(resolve => setTimeout(resolve, 100));

        console.log('🔧 Initializing GameEngine...');
        const gameEngine = new GameEngine(canvasRef.current);
        gameEngineRef.current = gameEngine;
        console.log('✅ GameEngine initialized');

        console.log('🎯 Initializing InputSystem...');
        const inputSystem = new InputSystem(gameEngine, canvasRef.current);
        inputSystemRef.current = inputSystem;
        console.log('✅ InputSystem initialized');

        console.log('🧱 Initializing BlockRenderer...');
        const blockRenderer = new BlockRenderer();
        blockRendererRef.current = blockRenderer;
        console.log('✅ BlockRenderer initialized');

        console.log('🌍 Initializing WorldManager...');
        const worldManager = new WorldManager(gameEngine, blockRenderer);
        worldManagerRef.current = worldManager;
        console.log('✅ WorldManager initialized');

        console.log('🔗 Initializing BlockInteraction...');
        const blockInteraction = new BlockInteraction(gameEngine, blockRenderer);
        blockInteractionRef.current = blockInteraction;
        console.log('✅ BlockInteraction initialized');

        console.log('🔌 Connecting systems...');
        inputSystem.setBlockInteraction(blockInteraction);

        gameEngine.addUpdateCallback(() => {
          worldManager.update();
          blockInteraction.update();
        });
        console.log('✅ Systems connected');

        console.log('🏗️ Generating test world...');
        worldManager.generateFlatTestWorld();
        console.log('✅ Test world generated');

        console.log('🚀 Starting game loop...');
        gameEngine.start();
        console.log('✅ Game loop started');

        // 添加一个小延迟确保第一帧渲染完成
        await new Promise(resolve => setTimeout(resolve, 100));

        console.log('🎉 Game initialization completed successfully!');
        setIsLoading(false);
      } catch (err) {
        console.error('❌ Failed to initialize game:', err);
        console.error('Error details:', {
          message: err instanceof Error ? err.message : 'Unknown error',
          stack: err instanceof Error ? err.stack : undefined
        });
        setError(err instanceof Error ? err.message : 'Unknown error');
        setIsLoading(false);
      }
    };

    // 延迟初始化确保组件完全挂载
    const timer = setTimeout(initializeGame, 500);

    // 清理函数
    return () => {
      clearTimeout(timer);

      if (gameEngineRef.current) {
        gameEngineRef.current.stop();
      }
      if (inputSystemRef.current) {
        inputSystemRef.current.dispose();
      }
      if (blockRendererRef.current) {
        blockRendererRef.current.dispose();
      }
      if (blockInteractionRef.current) {
        blockInteractionRef.current.dispose();
      }
      if (worldManagerRef.current) {
        worldManagerRef.current.dispose();
      }
    };
  }, []);



  if (error) {
    return (
      <div className="error-container">
        <h2>游戏加载失败</h2>
        <p>{error}</p>
        <button onClick={() => window.location.reload()}>
          重新加载
        </button>
      </div>
    );
  }

  return (
    <div className="game-container">
      {isLoading && (
        <div className="loading-overlay">
          <div className="loading-spinner"></div>
          <p>正在加载游戏...</p>
        </div>
      )}

      <canvas
        ref={canvasRef}
        className={className}
        style={{
          display: 'block',
          width: '100%',
          height: '100%'
        }}
      />

      {!isLoading && (
        <div className="game-ui">
          <div className="crosshair">+</div>
          <div className="instructions">
            <p>点击画面开始游戏</p>
            <p>WASD: 移动 | 空格: 跳跃 | Shift: 跑步</p>
            <p>鼠标: 视角 | 左键: 破坏 | 右键: 放置</p>
            <p>1-5: 选择方块类型 (草地/泥土/石头/木头/沙子)</p>
            <p>ESC: 退出鼠标锁定 | F: 全屏</p>
          </div>
        </div>
      )}
    </div>
  );
};
