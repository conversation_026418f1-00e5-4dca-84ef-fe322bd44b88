import React, { useRef, useEffect, useState } from 'react';
import { GameEngine } from '@/engine/GameEngine';
import { InputSystem } from '@/systems/InputSystem';
import { BlockRenderer } from '@/systems/BlockRenderer';
import { BlockInteraction } from '@/systems/BlockInteraction';
import { WorldManager } from '@/systems/WorldManager';
import { BlockType } from '@/types';
import * as THREE from 'three';

interface GameCanvasProps {
  className?: string;
}

export const GameCanvas: React.FC<GameCanvasProps> = ({ className }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const gameEngineRef = useRef<GameEngine | null>(null);
  const inputSystemRef = useRef<InputSystem | null>(null);
  const blockRendererRef = useRef<BlockRenderer | null>(null);
  const blockInteractionRef = useRef<BlockInteraction | null>(null);
  const worldManagerRef = useRef<WorldManager | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeGame = async () => {
      try {
        console.log('Initializing game...');

        if (!canvasRef.current) {
          throw new Error('Canvas not found');
        }

        // 初始化游戏引擎
        const gameEngine = new GameEngine(canvasRef.current);
        gameEngineRef.current = gameEngine;

        // 初始化输入系统
        const inputSystem = new InputSystem(gameEngine, canvasRef.current);
        inputSystemRef.current = inputSystem;

        // 初始化方块渲染器
        const blockRenderer = new BlockRenderer();
        blockRendererRef.current = blockRenderer;

        // 初始化世界管理器
        const worldManager = new WorldManager(gameEngine, blockRenderer);
        worldManagerRef.current = worldManager;

        // 初始化方块交互系统
        const blockInteraction = new BlockInteraction(gameEngine, blockRenderer);
        blockInteractionRef.current = blockInteraction;

        // 将方块交互系统连接到输入系统
        inputSystem.setBlockInteraction(blockInteraction);

        // 注册更新回调
        gameEngine.addUpdateCallback(() => {
          worldManager.update();
          blockInteraction.update();
        });

        // 生成测试世界
        worldManager.generateTestWorld();

        // 启动游戏循环
        gameEngine.start();

        console.log('Game initialized successfully');
        setIsLoading(false);
      } catch (err) {
        console.error('Failed to initialize game:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setIsLoading(false);
      }
    };

    initializeGame();

    // 清理函数
    return () => {
      if (gameEngineRef.current) {
        gameEngineRef.current.stop();
      }
      if (inputSystemRef.current) {
        inputSystemRef.current.dispose();
      }
      if (blockRendererRef.current) {
        blockRendererRef.current.dispose();
      }
      if (blockInteractionRef.current) {
        blockInteractionRef.current.dispose();
      }
      if (worldManagerRef.current) {
        worldManagerRef.current.dispose();
      }
    };
  }, []);



  if (error) {
    return (
      <div className="error-container">
        <h2>游戏加载失败</h2>
        <p>{error}</p>
        <button onClick={() => window.location.reload()}>
          重新加载
        </button>
      </div>
    );
  }

  return (
    <div className="game-container">
      {isLoading && (
        <div className="loading-overlay">
          <div className="loading-spinner"></div>
          <p>正在加载游戏...</p>
        </div>
      )}

      <canvas
        ref={canvasRef}
        className={className}
        style={{
          display: 'block',
          width: '100%',
          height: '100%'
        }}
      />

      {!isLoading && (
        <div className="game-ui">
          <div className="crosshair">+</div>
          <div className="instructions">
            <p>点击画面开始游戏</p>
            <p>WASD: 移动 | 空格: 跳跃 | Shift: 跑步</p>
            <p>鼠标: 视角 | 左键: 破坏 | 右键: 放置</p>
            <p>1-5: 选择方块类型 (草地/泥土/石头/木头/沙子)</p>
            <p>ESC: 退出鼠标锁定 | F: 全屏</p>
          </div>
        </div>
      )}
    </div>
  );
};
