import * as THREE from 'three';
import { BlockType } from '@/types';
import { TextureManager } from '@/systems/TextureManager';

export class BlockRenderer {
  private textureManager: TextureManager;
  private geometry: THREE.BoxGeometry;

  constructor() {
    this.geometry = new THREE.BoxGeometry(1, 1, 1);
    this.textureManager = new TextureManager();
  }



  public createBlockMesh(blockType: BlockType, position: THREE.Vector3): THREE.Mesh {
    const materials = this.textureManager.getMaterials(blockType);
    if (!materials || materials.length === 0) {
      throw new Error(`Materials not found for block type: ${blockType}`);
    }

    const mesh = new THREE.Mesh(this.geometry, materials);
    mesh.position.copy(position);
    mesh.castShadow = true;
    mesh.receiveShadow = true;

    return mesh;
  }

  public createChunkMesh(blocks: BlockType[][][], chunkX: number, chunkZ: number): THREE.Mesh {
    const geometry = new THREE.BufferGeometry();
    const vertices: number[] = [];
    const normals: number[] = [];
    const colors: number[] = [];
    const indices: number[] = [];

    let vertexIndex = 0;

    for (let x = 0; x < blocks.length; x++) {
      for (let y = 0; y < blocks[x].length; y++) {
        for (let z = 0; z < blocks[x][y].length; z++) {
          const blockType = blocks[x][y][z];
          
          if (blockType === BlockType.AIR) continue;

          const worldX = chunkX * 16 + x;
          const worldY = y;
          const worldZ = chunkZ * 16 + z;

          // 检查相邻方块，只渲染暴露的面
          const neighbors = {
            top: y + 1 < blocks[x].length ? blocks[x][y + 1][z] : BlockType.AIR,
            bottom: y - 1 >= 0 ? blocks[x][y - 1][z] : BlockType.BEDROCK,
            front: z + 1 < blocks[x][y].length ? blocks[x][y][z + 1] : BlockType.AIR,
            back: z - 1 >= 0 ? blocks[x][y][z - 1] : BlockType.AIR,
            right: x + 1 < blocks.length ? blocks[x + 1][y][z] : BlockType.AIR,
            left: x - 1 >= 0 ? blocks[x - 1][y][z] : BlockType.AIR
          };

          const color = this.getBlockColor(blockType);

          // 为每个暴露的面添加几何体
          this.addFaceIfExposed(
            vertices, normals, colors, indices,
            worldX, worldY, worldZ, color, vertexIndex,
            neighbors
          );

          vertexIndex += this.getVisibleFaceCount(neighbors) * 4;
        }
      }
    }

    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    geometry.setAttribute('normal', new THREE.Float32BufferAttribute(normals, 3));
    geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
    geometry.setIndex(indices);

    const material = new THREE.MeshLambertMaterial({ 
      vertexColors: true,
      transparent: false
    });

    const mesh = new THREE.Mesh(geometry, material);
    mesh.castShadow = true;
    mesh.receiveShadow = true;

    return mesh;
  }

  private getBlockColor(blockType: BlockType): THREE.Color {
    const colorMap = {
      [BlockType.GRASS]: new THREE.Color(0x7CB342),
      [BlockType.DIRT]: new THREE.Color(0x8D6E63),
      [BlockType.STONE]: new THREE.Color(0x757575),
      [BlockType.WOOD]: new THREE.Color(0x8D6E63),
      [BlockType.LEAVES]: new THREE.Color(0x4CAF50),
      [BlockType.SAND]: new THREE.Color(0xFFC107),
      [BlockType.WATER]: new THREE.Color(0x2196F3),
      [BlockType.BEDROCK]: new THREE.Color(0x424242)
    };

    return colorMap[blockType] || new THREE.Color(0xFFFFFF);
  }

  private addFaceIfExposed(
    vertices: number[], normals: number[], colors: number[], indices: number[],
    x: number, y: number, z: number, color: THREE.Color, vertexIndex: number,
    neighbors: any
  ): void {
    const faces = [
      { // Top
        condition: neighbors.top === BlockType.AIR,
        vertices: [
          [x - 0.5, y + 0.5, z - 0.5],
          [x + 0.5, y + 0.5, z - 0.5],
          [x + 0.5, y + 0.5, z + 0.5],
          [x - 0.5, y + 0.5, z + 0.5]
        ],
        normal: [0, 1, 0]
      },
      { // Bottom
        condition: neighbors.bottom === BlockType.AIR,
        vertices: [
          [x - 0.5, y - 0.5, z + 0.5],
          [x + 0.5, y - 0.5, z + 0.5],
          [x + 0.5, y - 0.5, z - 0.5],
          [x - 0.5, y - 0.5, z - 0.5]
        ],
        normal: [0, -1, 0]
      },
      { // Front
        condition: neighbors.front === BlockType.AIR,
        vertices: [
          [x - 0.5, y - 0.5, z + 0.5],
          [x - 0.5, y + 0.5, z + 0.5],
          [x + 0.5, y + 0.5, z + 0.5],
          [x + 0.5, y - 0.5, z + 0.5]
        ],
        normal: [0, 0, 1]
      },
      { // Back
        condition: neighbors.back === BlockType.AIR,
        vertices: [
          [x + 0.5, y - 0.5, z - 0.5],
          [x + 0.5, y + 0.5, z - 0.5],
          [x - 0.5, y + 0.5, z - 0.5],
          [x - 0.5, y - 0.5, z - 0.5]
        ],
        normal: [0, 0, -1]
      },
      { // Right
        condition: neighbors.right === BlockType.AIR,
        vertices: [
          [x + 0.5, y - 0.5, z + 0.5],
          [x + 0.5, y + 0.5, z + 0.5],
          [x + 0.5, y + 0.5, z - 0.5],
          [x + 0.5, y - 0.5, z - 0.5]
        ],
        normal: [1, 0, 0]
      },
      { // Left
        condition: neighbors.left === BlockType.AIR,
        vertices: [
          [x - 0.5, y - 0.5, z - 0.5],
          [x - 0.5, y + 0.5, z - 0.5],
          [x - 0.5, y + 0.5, z + 0.5],
          [x - 0.5, y - 0.5, z + 0.5]
        ],
        normal: [-1, 0, 0]
      }
    ];

    faces.forEach(face => {
      if (face.condition) {
        const startIndex = vertices.length / 3;

        // 添加顶点
        face.vertices.forEach(vertex => {
          vertices.push(...vertex);
          normals.push(...face.normal);
          colors.push(color.r, color.g, color.b);
        });

        // 添加索引（两个三角形组成一个面）
        indices.push(
          startIndex, startIndex + 1, startIndex + 2,
          startIndex, startIndex + 2, startIndex + 3
        );
      }
    });
  }

  private getVisibleFaceCount(neighbors: any): number {
    let count = 0;
    if (neighbors.top === BlockType.AIR) count++;
    if (neighbors.bottom === BlockType.AIR) count++;
    if (neighbors.front === BlockType.AIR) count++;
    if (neighbors.back === BlockType.AIR) count++;
    if (neighbors.right === BlockType.AIR) count++;
    if (neighbors.left === BlockType.AIR) count++;
    return count;
  }

  public dispose(): void {
    this.geometry.dispose();
    this.textureManager.dispose();
  }
}
