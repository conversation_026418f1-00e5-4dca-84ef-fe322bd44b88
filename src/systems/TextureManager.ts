import * as THREE from 'three';
import { BlockType } from '@/types';

export class TextureManager {
  private textureLoader: THREE.TextureLoader;
  private textures: Map<string, THREE.Texture>;
  private materials: Map<BlockType, THREE.Material[]>;

  constructor() {
    this.textureLoader = new THREE.TextureLoader();
    this.textures = new Map();
    this.materials = new Map();
    this.initializeTextures();
  }

  private initializeTextures(): void {
    // 创建程序化纹理而不是加载图片文件
    this.createProceduralTextures();
    this.createMaterials();
  }

  private createProceduralTextures(): void {
    // 草地纹理
    this.textures.set('grass_top', this.createGrassTexture());
    this.textures.set('grass_side', this.createGrassSideTexture());
    
    // 泥土纹理
    this.textures.set('dirt', this.createDirtTexture());
    
    // 石头纹理
    this.textures.set('stone', this.createStoneTexture());
    
    // 木头纹理
    this.textures.set('wood_top', this.createWoodTopTexture());
    this.textures.set('wood_side', this.createWoodSideTexture());
    
    // 树叶纹理
    this.textures.set('leaves', this.createLeavesTexture());
    
    // 沙子纹理
    this.textures.set('sand', this.createSandTexture());
    
    // 基岩纹理
    this.textures.set('bedrock', this.createBedrockTexture());
  }

  private createGrassTexture(): THREE.Texture {
    const canvas = document.createElement('canvas');
    canvas.width = 64;
    canvas.height = 64;
    const ctx = canvas.getContext('2d')!;
    
    // 基础绿色
    ctx.fillStyle = '#7CB342';
    ctx.fillRect(0, 0, 64, 64);
    
    // 添加草的细节
    for (let i = 0; i < 200; i++) {
      const x = Math.random() * 64;
      const y = Math.random() * 64;
      const shade = Math.random() * 0.3 + 0.7;
      ctx.fillStyle = `rgb(${Math.floor(124 * shade)}, ${Math.floor(179 * shade)}, ${Math.floor(66 * shade)})`;
      ctx.fillRect(x, y, 1, 1);
    }
    
    const texture = new THREE.CanvasTexture(canvas);
    texture.magFilter = THREE.NearestFilter;
    texture.minFilter = THREE.NearestFilter;
    return texture;
  }

  private createGrassSideTexture(): THREE.Texture {
    const canvas = document.createElement('canvas');
    canvas.width = 64;
    canvas.height = 64;
    const ctx = canvas.getContext('2d')!;
    
    // 上半部分绿色（草）
    ctx.fillStyle = '#7CB342';
    ctx.fillRect(0, 0, 64, 12);
    
    // 下半部分棕色（泥土）
    ctx.fillStyle = '#8D6E63';
    ctx.fillRect(0, 12, 64, 52);
    
    // 添加细节
    for (let i = 0; i < 100; i++) {
      const x = Math.random() * 64;
      const y = Math.random() * 64;
      const shade = Math.random() * 0.3 + 0.7;
      if (y < 12) {
        ctx.fillStyle = `rgb(${Math.floor(124 * shade)}, ${Math.floor(179 * shade)}, ${Math.floor(66 * shade)})`;
      } else {
        ctx.fillStyle = `rgb(${Math.floor(141 * shade)}, ${Math.floor(110 * shade)}, ${Math.floor(99 * shade)})`;
      }
      ctx.fillRect(x, y, 1, 1);
    }
    
    const texture = new THREE.CanvasTexture(canvas);
    texture.magFilter = THREE.NearestFilter;
    texture.minFilter = THREE.NearestFilter;
    return texture;
  }

  private createDirtTexture(): THREE.Texture {
    const canvas = document.createElement('canvas');
    canvas.width = 64;
    canvas.height = 64;
    const ctx = canvas.getContext('2d')!;
    
    ctx.fillStyle = '#8D6E63';
    ctx.fillRect(0, 0, 64, 64);
    
    // 添加泥土颗粒
    for (let i = 0; i < 300; i++) {
      const x = Math.random() * 64;
      const y = Math.random() * 64;
      const shade = Math.random() * 0.4 + 0.6;
      ctx.fillStyle = `rgb(${Math.floor(141 * shade)}, ${Math.floor(110 * shade)}, ${Math.floor(99 * shade)})`;
      ctx.fillRect(x, y, Math.random() * 2 + 1, Math.random() * 2 + 1);
    }
    
    const texture = new THREE.CanvasTexture(canvas);
    texture.magFilter = THREE.NearestFilter;
    texture.minFilter = THREE.NearestFilter;
    return texture;
  }

  private createStoneTexture(): THREE.Texture {
    const canvas = document.createElement('canvas');
    canvas.width = 64;
    canvas.height = 64;
    const ctx = canvas.getContext('2d')!;
    
    ctx.fillStyle = '#757575';
    ctx.fillRect(0, 0, 64, 64);
    
    // 添加石头纹理
    for (let i = 0; i < 400; i++) {
      const x = Math.random() * 64;
      const y = Math.random() * 64;
      const shade = Math.random() * 0.5 + 0.5;
      ctx.fillStyle = `rgb(${Math.floor(117 * shade)}, ${Math.floor(117 * shade)}, ${Math.floor(117 * shade)})`;
      ctx.fillRect(x, y, 1, 1);
    }
    
    const texture = new THREE.CanvasTexture(canvas);
    texture.magFilter = THREE.NearestFilter;
    texture.minFilter = THREE.NearestFilter;
    return texture;
  }

  private createWoodTopTexture(): THREE.Texture {
    const canvas = document.createElement('canvas');
    canvas.width = 64;
    canvas.height = 64;
    const ctx = canvas.getContext('2d')!;
    
    ctx.fillStyle = '#8D6E63';
    ctx.fillRect(0, 0, 64, 64);
    
    // 年轮
    const centerX = 32;
    const centerY = 32;
    for (let r = 5; r < 30; r += 3) {
      ctx.strokeStyle = `rgba(139, 69, 19, ${0.3 + Math.random() * 0.3})`;
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.arc(centerX, centerY, r, 0, Math.PI * 2);
      ctx.stroke();
    }
    
    const texture = new THREE.CanvasTexture(canvas);
    texture.magFilter = THREE.NearestFilter;
    texture.minFilter = THREE.NearestFilter;
    return texture;
  }

  private createWoodSideTexture(): THREE.Texture {
    const canvas = document.createElement('canvas');
    canvas.width = 64;
    canvas.height = 64;
    const ctx = canvas.getContext('2d')!;
    
    ctx.fillStyle = '#8D6E63';
    ctx.fillRect(0, 0, 64, 64);
    
    // 木纹
    for (let y = 0; y < 64; y += 2) {
      const shade = 0.8 + Math.random() * 0.4;
      ctx.fillStyle = `rgba(139, 69, 19, ${shade})`;
      ctx.fillRect(0, y, 64, 1);
    }
    
    const texture = new THREE.CanvasTexture(canvas);
    texture.magFilter = THREE.NearestFilter;
    texture.minFilter = THREE.NearestFilter;
    return texture;
  }

  private createLeavesTexture(): THREE.Texture {
    const canvas = document.createElement('canvas');
    canvas.width = 64;
    canvas.height = 64;
    const ctx = canvas.getContext('2d')!;
    
    ctx.fillStyle = '#4CAF50';
    ctx.fillRect(0, 0, 64, 64);
    
    // 叶子细节
    for (let i = 0; i < 500; i++) {
      const x = Math.random() * 64;
      const y = Math.random() * 64;
      const shade = Math.random() * 0.6 + 0.4;
      ctx.fillStyle = `rgb(${Math.floor(76 * shade)}, ${Math.floor(175 * shade)}, ${Math.floor(80 * shade)})`;
      ctx.fillRect(x, y, 1, 1);
    }
    
    const texture = new THREE.CanvasTexture(canvas);
    texture.magFilter = THREE.NearestFilter;
    texture.minFilter = THREE.NearestFilter;
    return texture;
  }

  private createSandTexture(): THREE.Texture {
    const canvas = document.createElement('canvas');
    canvas.width = 64;
    canvas.height = 64;
    const ctx = canvas.getContext('2d')!;
    
    ctx.fillStyle = '#FFC107';
    ctx.fillRect(0, 0, 64, 64);
    
    // 沙粒
    for (let i = 0; i < 600; i++) {
      const x = Math.random() * 64;
      const y = Math.random() * 64;
      const shade = Math.random() * 0.3 + 0.7;
      ctx.fillStyle = `rgb(${Math.floor(255 * shade)}, ${Math.floor(193 * shade)}, ${Math.floor(7 * shade)})`;
      ctx.fillRect(x, y, 1, 1);
    }
    
    const texture = new THREE.CanvasTexture(canvas);
    texture.magFilter = THREE.NearestFilter;
    texture.minFilter = THREE.NearestFilter;
    return texture;
  }

  private createBedrockTexture(): THREE.Texture {
    const canvas = document.createElement('canvas');
    canvas.width = 64;
    canvas.height = 64;
    const ctx = canvas.getContext('2d')!;
    
    ctx.fillStyle = '#424242';
    ctx.fillRect(0, 0, 64, 64);
    
    // 基岩纹理
    for (let i = 0; i < 200; i++) {
      const x = Math.random() * 64;
      const y = Math.random() * 64;
      const shade = Math.random() * 0.4 + 0.3;
      ctx.fillStyle = `rgb(${Math.floor(66 * shade)}, ${Math.floor(66 * shade)}, ${Math.floor(66 * shade)})`;
      ctx.fillRect(x, y, Math.random() * 3 + 1, Math.random() * 3 + 1);
    }
    
    const texture = new THREE.CanvasTexture(canvas);
    texture.magFilter = THREE.NearestFilter;
    texture.minFilter = THREE.NearestFilter;
    return texture;
  }

  private createMaterials(): void {
    // 草地方块 - 顶部、底部、侧面不同纹理
    this.materials.set(BlockType.GRASS, [
      new THREE.MeshLambertMaterial({ map: this.textures.get('grass_side') }), // 右
      new THREE.MeshLambertMaterial({ map: this.textures.get('grass_side') }), // 左
      new THREE.MeshLambertMaterial({ map: this.textures.get('grass_top') }),  // 顶
      new THREE.MeshLambertMaterial({ map: this.textures.get('dirt') }),       // 底
      new THREE.MeshLambertMaterial({ map: this.textures.get('grass_side') }), // 前
      new THREE.MeshLambertMaterial({ map: this.textures.get('grass_side') })  // 后
    ]);

    // 泥土方块
    const dirtMaterial = new THREE.MeshLambertMaterial({ map: this.textures.get('dirt') });
    this.materials.set(BlockType.DIRT, [dirtMaterial, dirtMaterial, dirtMaterial, dirtMaterial, dirtMaterial, dirtMaterial]);

    // 石头方块
    const stoneMaterial = new THREE.MeshLambertMaterial({ map: this.textures.get('stone') });
    this.materials.set(BlockType.STONE, [stoneMaterial, stoneMaterial, stoneMaterial, stoneMaterial, stoneMaterial, stoneMaterial]);

    // 木头方块
    this.materials.set(BlockType.WOOD, [
      new THREE.MeshLambertMaterial({ map: this.textures.get('wood_side') }), // 右
      new THREE.MeshLambertMaterial({ map: this.textures.get('wood_side') }), // 左
      new THREE.MeshLambertMaterial({ map: this.textures.get('wood_top') }),  // 顶
      new THREE.MeshLambertMaterial({ map: this.textures.get('wood_top') }),  // 底
      new THREE.MeshLambertMaterial({ map: this.textures.get('wood_side') }), // 前
      new THREE.MeshLambertMaterial({ map: this.textures.get('wood_side') })  // 后
    ]);

    // 树叶方块
    const leavesMaterial = new THREE.MeshLambertMaterial({ 
      map: this.textures.get('leaves'),
      transparent: true,
      opacity: 0.8
    });
    this.materials.set(BlockType.LEAVES, [leavesMaterial, leavesMaterial, leavesMaterial, leavesMaterial, leavesMaterial, leavesMaterial]);

    // 沙子方块
    const sandMaterial = new THREE.MeshLambertMaterial({ map: this.textures.get('sand') });
    this.materials.set(BlockType.SAND, [sandMaterial, sandMaterial, sandMaterial, sandMaterial, sandMaterial, sandMaterial]);

    // 基岩方块
    const bedrockMaterial = new THREE.MeshLambertMaterial({ map: this.textures.get('bedrock') });
    this.materials.set(BlockType.BEDROCK, [bedrockMaterial, bedrockMaterial, bedrockMaterial, bedrockMaterial, bedrockMaterial, bedrockMaterial]);
  }

  public getMaterials(blockType: BlockType): THREE.Material[] {
    return this.materials.get(blockType) || [];
  }

  public dispose(): void {
    this.textures.forEach(texture => texture.dispose());
    this.materials.forEach(materials => {
      materials.forEach(material => material.dispose());
    });
    this.textures.clear();
    this.materials.clear();
  }
}
