import { BlockType, WorldGenConfig, Chunk, CHUNK_SIZE } from '@/types';

export class WorldGenerator {
  private config: WorldGenConfig;

  constructor(config?: Partial<WorldGenConfig>) {
    this.config = {
      seed: 12345,
      terrainScale: 0.03, // 更细腻的地形
      terrainHeight: 25,  // 更高的山丘
      treeFrequency: 0.03, // 更多树木
      oreFrequency: 0.15,  // 更多矿物变化
      ...config
    };
  }

  public generateChunk(chunkX: number, chunkZ: number): Chunk {
    const blocks: BlockType[][][] = [];
    
    // 初始化三维数组
    for (let x = 0; x < CHUNK_SIZE; x++) {
      blocks[x] = [];
      for (let y = 0; y < 256; y++) {
        blocks[x][y] = [];
        for (let z = 0; z < CHUNK_SIZE; z++) {
          blocks[x][y][z] = BlockType.AIR;
        }
      }
    }

    // 生成地形
    this.generateTerrain(blocks, chunkX, chunkZ);
    
    // 生成矿物
    this.generateOres(blocks, chunkX, chunkZ);
    
    // 生成树木
    this.generateTrees(blocks, chunkX, chunkZ);

    return {
      x: chunkX,
      z: chunkZ,
      blocks,
      needsUpdate: true
    };
  }

  private generateTerrain(blocks: BlockType[][][], chunkX: number, chunkZ: number): void {
    for (let x = 0; x < CHUNK_SIZE; x++) {
      for (let z = 0; z < CHUNK_SIZE; z++) {
        const worldX = chunkX * CHUNK_SIZE + x;
        const worldZ = chunkZ * CHUNK_SIZE + z;
        
        // 使用简化的噪声函数生成高度
        const height = this.getTerrainHeight(worldX, worldZ);
        
        for (let y = 0; y <= height; y++) {
          if (y === 0) {
            // 基岩层
            blocks[x][y][z] = BlockType.BEDROCK;
          } else if (y < height - 4) {
            // 深层石头
            blocks[x][y][z] = BlockType.STONE;
          } else if (y < height - 1) {
            // 泥土层，厚度根据高度变化
            blocks[x][y][z] = BlockType.DIRT;
          } else if (y === height) {
            // 表面层 - 根据高度和位置决定方块类型
            if (height > 75) {
              // 高山 - 石头
              blocks[x][y][z] = BlockType.STONE;
            } else if (height > 65) {
              // 丘陵 - 草地
              blocks[x][y][z] = BlockType.GRASS;
            } else if (height > 55) {
              // 平原 - 草地
              blocks[x][y][z] = BlockType.GRASS;
            } else if (height > 45) {
              // 海滩 - 沙子
              blocks[x][y][z] = BlockType.SAND;
            } else {
              // 低地 - 石头
              blocks[x][y][z] = BlockType.STONE;
            }
          }
        }
      }
    }
  }

  private generateOres(blocks: BlockType[][][], chunkX: number, chunkZ: number): void {
    for (let x = 0; x < CHUNK_SIZE; x++) {
      for (let y = 1; y < 50; y++) {
        for (let z = 0; z < CHUNK_SIZE; z++) {
          if (blocks[x][y][z] === BlockType.STONE) {
            const worldX = chunkX * CHUNK_SIZE + x;
            const worldZ = chunkZ * CHUNK_SIZE + z;
            
            // 简单的矿物生成
            if (this.noise3D(worldX, y, worldZ, 0.1) > 0.7) {
              // 根据深度生成不同类型的"矿物"（用不同颜色的石头表示）
              if (y < 20) {
                blocks[x][y][z] = BlockType.BEDROCK; // 深层矿物
              } else {
                blocks[x][y][z] = BlockType.STONE; // 保持石头
              }
            }
          }
        }
      }
    }
  }

  private generateTrees(blocks: BlockType[][][], chunkX: number, chunkZ: number): void {
    for (let x = 2; x < CHUNK_SIZE - 2; x++) {
      for (let z = 2; z < CHUNK_SIZE - 2; z++) {
        const worldX = chunkX * CHUNK_SIZE + x;
        const worldZ = chunkZ * CHUNK_SIZE + z;
        
        // 检查是否应该生成树 - 只在草地上生成
        if (this.noise2D(worldX, worldZ, 0.02) > 0.85) {
          // 找到地面高度
          let groundHeight = -1;
          for (let y = 255; y >= 0; y--) {
            if (blocks[x][y][z] === BlockType.GRASS) {
              groundHeight = y;
              break;
            }
          }

          // 只在合适的高度和草地上生成树
          if (groundHeight > 55 && groundHeight < 85) {
            this.generateTree(blocks, x, groundHeight + 1, z);
          }
        }
      }
    }
  }

  private generateTree(blocks: BlockType[][][], x: number, startY: number, z: number): void {
    const treeHeight = 4 + Math.floor(Math.random() * 4); // 4-7格高

    // 生成树干
    for (let y = startY; y < startY + treeHeight; y++) {
      if (y < 256) {
        blocks[x][y][z] = BlockType.WOOD;
      }
    }

    // 生成更自然的树冠
    const leafY = startY + treeHeight;

    // 树冠分层生成
    for (let layer = -2; layer <= 1; layer++) {
      const currentY = leafY + layer;
      let radius = 2;

      // 顶层较小
      if (layer === 1) radius = 1;
      // 中层最大
      if (layer === 0 || layer === -1) radius = 2;

      for (let dx = -radius; dx <= radius; dx++) {
        for (let dz = -radius; dz <= radius; dz++) {
          const leafX = x + dx;
          const leafZ = z + dz;

          if (leafX >= 0 && leafX < CHUNK_SIZE &&
              leafZ >= 0 && leafZ < CHUNK_SIZE &&
              currentY >= 0 && currentY < 256) {

            // 跳过树干位置
            if (dx === 0 && dz === 0 && layer <= 0) {
              continue;
            }

            // 圆形树冠，边角概率性生成
            const distance = Math.sqrt(dx * dx + dz * dz);
            if (distance <= radius) {
              // 边缘位置有概率不生成，使树冠更自然
              if (distance < radius - 0.5 || Math.random() > 0.3) {
                if (blocks[leafX][currentY][leafZ] === BlockType.AIR) {
                  blocks[leafX][currentY][leafZ] = BlockType.LEAVES;
                }
              }
            }
          }
        }
      }
    }
  }

  private getTerrainHeight(x: number, z: number): number {
    const baseHeight = 64;
    const amplitude = this.config.terrainHeight;
    
    // 多层噪声叠加
    let height = baseHeight;
    height += this.noise2D(x, z, this.config.terrainScale) * amplitude;
    height += this.noise2D(x, z, this.config.terrainScale * 2) * amplitude * 0.5;
    height += this.noise2D(x, z, this.config.terrainScale * 4) * amplitude * 0.25;
    
    return Math.floor(Math.max(1, Math.min(120, height)));
  }

  // 简化的2D噪声函数
  private noise2D(x: number, z: number, scale: number): number {
    x *= scale;
    z *= scale;
    
    // 简单的伪随机噪声
    const n = Math.sin(x * 12.9898 + z * 78.233) * 43758.5453;
    return (n - Math.floor(n)) * 2 - 1;
  }

  // 简化的3D噪声函数
  private noise3D(x: number, y: number, z: number, scale: number): number {
    x *= scale;
    y *= scale;
    z *= scale;
    
    // 简单的伪随机噪声
    const n = Math.sin(x * 12.9898 + y * 78.233 + z * 37.719) * 43758.5453;
    return (n - Math.floor(n)) * 2 - 1;
  }

  public generateFlatWorld(chunkX: number, chunkZ: number, height: number = 4): Chunk {
    const blocks: BlockType[][][] = [];
    
    // 初始化三维数组
    for (let x = 0; x < CHUNK_SIZE; x++) {
      blocks[x] = [];
      for (let y = 0; y < 256; y++) {
        blocks[x][y] = [];
        for (let z = 0; z < CHUNK_SIZE; z++) {
          if (y === 0) {
            blocks[x][y][z] = BlockType.BEDROCK;
          } else if (y < height - 1) {
            blocks[x][y][z] = BlockType.DIRT;
          } else if (y === height - 1) {
            blocks[x][y][z] = BlockType.GRASS;
          } else {
            blocks[x][y][z] = BlockType.AIR;
          }
        }
      }
    }

    return {
      x: chunkX,
      z: chunkZ,
      blocks,
      needsUpdate: true
    };
  }

  public setConfig(config: Partial<WorldGenConfig>): void {
    this.config = { ...this.config, ...config };
  }

  public getConfig(): WorldGenConfig {
    return { ...this.config };
  }
}
